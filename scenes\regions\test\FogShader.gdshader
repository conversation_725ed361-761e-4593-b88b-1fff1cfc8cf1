shader_type canvas_item;

// Konfigurowalne kolory mgły
uniform vec4 dark_color : source_color = vec4(0.1, 0.4, 0.2, 0.85);
uniform vec4 light_color : source_color = vec4(0.2, 0.6, 0.3, 0.65);
uniform vec4 mist_color : source_color = vec4(0.3, 0.7, 0.4, 0.45);

// Parametry mgły
uniform float fog_density : hint_range(0.0, 1.0) = 0.8;
uniform float time : hint_range(0.0, 100.0) = 0.0;
uniform bool enable_drift = true;

// Funkcja szumu dla organicznego wyglądu
float hash(vec2 p) {
    return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453);
}

float noise(vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(
        mix(hash(i + vec2(0.0, 0.0)), hash(i + vec2(1.0, 0.0)), u.x),
        mix(hash(i + vec2(0.0, 1.0)), hash(i + vec2(1.0, 1.0)), u.x),
        u.y
    );
}

// Wielowarstwowy szum dla głębi
float layered_noise(vec2 p) {
    float value = 0.0;
    float amplitude = 0.5;
    
    // 3 warstwy szumu dla bogatszego efektu
    for (int i = 0; i < 3; i++) {
        value += amplitude * noise(p);
        p *= 2.0;
        amplitude *= 0.5;
    }
    
    return value;
}

void fragment() {
    vec2 uv = UV;
    
    // Podstawowy szum dla tekstury mgły
    vec2 noise_uv = uv * 4.0;
    
    // Bardzo subtelny ruch jeśli włączony
    if (enable_drift) {
        noise_uv += vec2(time * 0.01, time * 0.005);
    }
    
    float base_noise = layered_noise(noise_uv);
    
    // Większa gęstość w dolnej części (jak na obrazku)
    float vertical_gradient = 1.0 - (uv.y * 0.3);
    
    // Miękkie krawędzie
    float edge_distance = min(min(uv.x, 1.0 - uv.x), min(uv.y, 1.0 - uv.y));
    float edge_fade = smoothstep(0.0, 0.1, edge_distance);
    
    // Kombinacja wszystkich czynników
    float fog_amount = base_noise * vertical_gradient * fog_density * edge_fade;
    fog_amount = clamp(fog_amount, 0.0, 1.0);
    
    // Mieszanie kolorów na podstawie gęstości
    vec4 fog_color;
    
    if (fog_amount > 0.7) {
        // Bardzo gęsta mgła - ciemny kolor
        fog_color = dark_color;
    } else if (fog_amount > 0.4) {
        // Średnia gęstość - mix ciemnego i jasnego
        float mix_factor = (fog_amount - 0.4) / 0.3;
        fog_color = mix(light_color, dark_color, mix_factor);
    } else {
        // Lekka mgiełka
        float mix_factor = fog_amount / 0.4;
        fog_color = mix(vec4(mist_color.rgb, 0.0), light_color, mix_factor);
    }
    
    // Zastosuj końcową przezroczystość
    fog_color.a *= fog_amount;
    
    // Subtelne "oddychanie" mgły
    float breathing = sin(time * 0.3) * 0.05 + 1.0;
    fog_color.a *= breathing;
    
    COLOR = fog_color;
}