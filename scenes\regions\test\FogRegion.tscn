[gd_scene load_steps=3 format=3 uid="uid://dfp765calwhpo"]

[ext_resource type="Script" uid="uid://bs88bw7arw4ul" path="res://scenes/regions/test/AtmosphericFogRegion.cs" id="1_1kn1j"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2buph"]
size = Vector2(703, 555)

[node name="SimpleFogRegion" type="Area2D"]
z_index = 1
scale = Vector2(0.5, 0.5)
script = ExtResource("1_1kn1j")
DarkColor = Color(0.226707, 0.226707, 0.226707, 0.705882)
LightColor = Color(0.498039, 0.498039, 0.498039, 0.635294)
MistColor = Color(1, 1, 1, 0.592157)
FogDensity = 0.3
ParticleSpeed = 100.0
ParticleCount = 50

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(18.5, 200.5)
shape = SubResource("RectangleShape2D_2buph")
