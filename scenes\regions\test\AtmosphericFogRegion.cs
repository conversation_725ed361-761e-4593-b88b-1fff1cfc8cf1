using Godot;

[GlobalClass]
public partial class AtmosphericFogRegion : Area2D
{
	[Export] public Color DarkColor { get; set; } = new Color(0.1f, 0.4f, 0.2f, 0.85f); // Ciemny podstawowy
	[Export] public Color LightColor { get; set; } = new Color(0.2f, 0.6f, 0.3f, 0.65f); // Jaśniejszy podstawowy
	[Export] public Color MistColor { get; set; } = new Color(0.3f, 0.7f, 0.4f, 0.45f); // Lekka mgiełka
	[Export] public float FogDensity { get; set; } = 0.8f;
	[Export] public float ParticleSpeed { get; set; } = 15.0f;
	[Export] public int ParticleCount { get; set; } = 120;
	[Export] public bool EnableSubtleDrift { get; set; } = true;
	[Export] public bool EnableParticles { get; set; } = true;
	
	private AtmosphericFogRenderer _fogRenderer;
	private AtmosphericParticleSystem _particleSystem;
	private CollisionShape2D _collisionShape;
	private float _time = 0.0f;
	
	public override void _Ready()
	{
		_collisionShape = GetNode<CollisionShape2D>("CollisionShape2D");
		
		// Initialize fog renderer
		_fogRenderer = new AtmosphericFogRenderer();
		AddChild(_fogRenderer);
		_fogRenderer.InitializeFog(this);
		
		// Ważne: ustaw clipping na Area2D
		//ClipContents = true;
		
		// Initialize particles
		if (EnableParticles)
		{
			_particleSystem = new AtmosphericParticleSystem();
			AddChild(_particleSystem);
			_particleSystem.InitializeParticles(this);
		}
		
		BodyEntered += OnBodyEntered;
		BodyExited += OnBodyExited;
	}
	
	public override void _Process(double delta)
	{
		_time += (float)delta;
		_fogRenderer?.UpdateFog(_time);
	}
	
	private void OnBodyEntered(Node2D body)
	{
		if (body.IsInGroup("player"))
		{
			GD.Print("Gracz wszedł w zieloną mgłę");
		}
	}
	
	private void OnBodyExited(Node2D body)
	{
		if (body.IsInGroup("player"))
		{
			GD.Print("Gracz wyszedł z zielonej mgły");
		}
	}
	
	public Rect2 GetFogBounds()
	{
		if (_collisionShape?.Shape is RectangleShape2D rect)
		{
			var size = rect.Size;
			return new Rect2(GlobalPosition - size / 2, size);
		}
		return new Rect2(GlobalPosition, Vector2.One * 300);
	}
}

[GlobalClass]
public partial class AtmosphericFogRenderer : Node2D
{
	private ColorRect _fogCanvas;
	private ShaderMaterial _fogMaterial;
	private AtmosphericFogRegion _parentRegion;
	
	public void InitializeFog(AtmosphericFogRegion parent)
	{
		_parentRegion = parent;
		
		_fogCanvas = new ColorRect();
		AddChild(_fogCanvas);
		
		var fogBounds = parent.GetFogBounds();
		_fogCanvas.Position = Vector2.Zero; // Relatywnie do Area2D
		_fogCanvas.Size = fogBounds.Size;
		
		// Ustaw clipping żeby mgła nie wychodziła poza obszar
		_fogCanvas.ClipContents = true;
		
		// Load atmospheric fog shader
		var shader = GD.Load<Shader>("res://scenes/regions/test/FogShader.gdshader");
		_fogMaterial = new ShaderMaterial();
		_fogMaterial.Shader = shader;
		
		_fogCanvas.Material = _fogMaterial;
		
		// Set fog parameters
		_fogMaterial.SetShaderParameter("dark_color", parent.DarkColor);
		_fogMaterial.SetShaderParameter("light_color", parent.LightColor);
		_fogMaterial.SetShaderParameter("mist_color", parent.MistColor);
		_fogMaterial.SetShaderParameter("fog_density", parent.FogDensity);
		_fogMaterial.SetShaderParameter("enable_drift", parent.EnableSubtleDrift);
	}
	
	public void UpdateFog(float time)
	{
		if (_fogMaterial != null)
		{
			_fogMaterial.SetShaderParameter("time", time);
		}
	}
}

[GlobalClass]
public partial class AtmosphericParticleSystem : Node2D
{
	private GpuParticles2D _mistParticles;
	private GpuParticles2D _denseParticles;
	private AtmosphericFogRegion _parentRegion;
	
	public void InitializeParticles(AtmosphericFogRegion parent)
	{
		_parentRegion = parent;
		var fogBounds = parent.GetFogBounds();
		
		// Dense fog particles (like in the image)
		_denseParticles = new GpuParticles2D();
		_denseParticles.Name = "DenseFogParticles";
		_denseParticles.Amount = parent.ParticleCount;
		_denseParticles.Lifetime = 12.0f;
		_denseParticles.Emitting = true;
		AddChild(_denseParticles);
		
		// Light mist particles
		_mistParticles = new GpuParticles2D();
		_mistParticles.Name = "MistParticles";
		_mistParticles.Amount = parent.ParticleCount / 2;
		_mistParticles.Lifetime = 15.0f;
		_mistParticles.Emitting = true;
		AddChild(_mistParticles);
		
		SetupDenseParticles(_denseParticles, fogBounds);
		SetupMistParticles(_mistParticles, fogBounds);
	}
	
	private void SetupDenseParticles(GpuParticles2D particles, Rect2 bounds)
	{
		var material = new ParticleProcessMaterial();
		
		// Emission tylko w obszarze bounds
		material.EmissionShape = ParticleProcessMaterial.EmissionShapeEnum.Box;
		material.EmissionBoxExtents = new Vector3(bounds.Size.X / 2, bounds.Size.Y / 2, 0);
		
		// Powolny ruch w prawo
		material.Direction = new Vector3(1, 0, 0);
		material.InitialVelocityMin = _parentRegion.ParticleSpeed * 0.2f;
		material.InitialVelocityMax = _parentRegion.ParticleSpeed * 0.5f;
		
		// Large, billowy particles
		material.ScaleMin = 1.5f;
		material.ScaleMax = 3.0f;
		
		// Kolorowe cząsteczki
		material.Color = _parentRegion.LightColor;
		
		// Rotation
		material.AngularVelocityMin = -10.0f;
		material.AngularVelocityMax = 10.0f;
		
		// Brak grawitacji
		material.Gravity = Vector3.Zero;
		
		// WAŻNE: Ustaw visibility rect żeby cząsteczki znikały poza obszarem
		particles.VisibilityRect = new Rect2(-bounds.Size.X/2, -bounds.Size.Y/2, bounds.Size.X, bounds.Size.Y);
		
		particles.ProcessMaterial = material;
		particles.Texture = CreateAtmosphericTexture(64, 0.3f);
	}
	
	private void SetupMistParticles(GpuParticles2D particles, Rect2 bounds)
	{
		var material = new ParticleProcessMaterial();
		
		material.EmissionShape = ParticleProcessMaterial.EmissionShapeEnum.Box;
		material.EmissionBoxExtents = new Vector3(bounds.Size.X / 2, bounds.Size.Y / 2, 0);
		
		// Powolny ruch w prawo
		material.Direction = new Vector3(0.5f, 0, 0);
		material.InitialVelocityMin = _parentRegion.ParticleSpeed * 0.1f;
		material.InitialVelocityMax = _parentRegion.ParticleSpeed * 0.3f;
		
		// Medium-sized wispy particles
		material.ScaleMin = 0.8f;
		material.ScaleMax = 2.0f;
		
		// Kolorowe cząsteczki mgiełki
		material.Color = _parentRegion.MistColor;
		
		material.AngularVelocityMin = -5.0f;
		material.AngularVelocityMax = 5.0f;
		
		// Brak grawitacji
		material.Gravity = Vector3.Zero;
		
		// WAŻNE: Visibility rect dla mist particles
		particles.VisibilityRect = new Rect2(-bounds.Size.X/2, -bounds.Size.Y/2, bounds.Size.X, bounds.Size.Y);
		
		particles.ProcessMaterial = material;
		particles.Texture = CreateAtmosphericTexture(32, 0.5f);
	}
	
	private ImageTexture CreateAtmosphericTexture(int size, float density)
	{
		var image = Image.Create(size, size, false, Image.Format.Rgba8);
		var center = size / 2.0f;
		
		for (int x = 0; x < size; x++)
		{
			for (int y = 0; y < size; y++)
			{
				var distance = Mathf.Sqrt((x - center) * (x - center) + (y - center) * (y - center));
				var normalizedDistance = distance / center;
				
				// Soft falloff for atmospheric look
				var alpha = Mathf.Max(0, 1.0f - normalizedDistance * normalizedDistance) * density;
				
				// Add some organic variation
				var noise = Mathf.Sin(x * 0.2f) * Mathf.Sin(y * 0.2f) * 0.1f;
				alpha += noise;
				alpha = Mathf.Clamp(alpha, 0.0f, 1.0f);
				
				// Cząsteczka w kolorze mgły
				image.SetPixel(x, y, new Color(1.0f, 1.0f, 1.0f, alpha));
			}
		}
		
		return ImageTexture.CreateFromImage(image);
	}
}
