<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4b6333d1-bc1d-4a0d-8601-7e56084ca5f9" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Temp/SourceGeneratedDocuments/313F1B770352D454CAEC6A5F/Godot.SourceGenerators/Godot.SourceGenerators.ScriptMethodsGenerator/CustomDataLayerManager_ScriptMethods.generated.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/69e3acf0074a4ef8afaea275d4055b96522800/a3/7822951e/StringName.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/23687fc69b48c25e8e35832be892d74e7bf23085/Tree_ScriptMethods.generated.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bda9cf3bf2391fc2775717bcb1e3e92f74d886/ResourcesManager_ScriptMethods.generated.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/df73a4db74df89d59655c5fb6326406f47fbfa9af1fa81518fe0a07c49d34133/Node.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/e75a5575ba872c8ea754c015cb363850e6c661f39569712d5b74aaca67263c/String.Manipulation.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/ef7b819b226fab796d1dfe66d415dd7510bcac87675020ddb8f03a828e763/CanvasItem.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2xtlnJOqp3zI337ahWPmIuevmLG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET Executable.Player.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET Executable.Player">
    <configuration name="Player GDScript" type="GDSCRIPT_DEBUG_RUN_CONFIGURATION" factoryName="GdScriptRunFactory" show_console_on_std_err="false" show_console_on_std_out="false" port="6006">
      <option name="address" value="127.0.0.1" />
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="Editor" type="RunExe" factoryName=".NET Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/../../Godot/Godot_v4.4.1-stable_mono_win64/Godot_v4.4.1-stable_mono_win64.exe" />
      <option name="PROGRAM_PARAMETERS" value="--path &quot;./&quot; --editor" />
      <option name="WORKING_DIRECTORY" value="C:\dev\repos\crafterio" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="RUNTIME_TYPE" value="coreclr" />
      <method v="2">
        <option name="Build Solution" enabled="true" />
      </method>
    </configuration>
    <configuration name="Player" type="RunExe" factoryName=".NET Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/../../Godot/Godot_v4.4.1-stable_mono_win64/Godot_v4.4.1-stable_mono_win64.exe" />
      <option name="PROGRAM_PARAMETERS" value="--path &quot;./&quot;" />
      <option name="WORKING_DIRECTORY" value="C:\dev\repos\crafterio" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="RUNTIME_TYPE" value="coreclr" />
      <method v="2">
        <option name="Build Solution" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4b6333d1-bc1d-4a0d-8601-7e56084ca5f9" name="Changes" comment="" />
      <created>1748765416287</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748765416287</updated>
      <workItem from="1748765419933" duration="4925000" />
      <workItem from="1749230017061" duration="4313000" />
      <workItem from="1750598114914" duration="8149000" />
      <workItem from="1752088444119" duration="9425000" />
      <workItem from="1753346189839" duration="18000" />
      <workItem from="1753382716847" duration="594000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/ResourcesManager.cs</url>
          <line>141</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\ResourcesManager.cs" containingFunctionPresentation="Method 'SetExperience'">
            <startOffsets>
              <option value="4342" />
            </startOffsets>
            <endOffsets>
              <option value="4343" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/mapObjects/Chest.cs</url>
          <line>276</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\mapObjects\Chest.cs" containingFunctionPresentation="Method 'SpawnChest'">
            <startOffsets>
              <option value="6535" />
            </startOffsets>
            <endOffsets>
              <option value="6611" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryMenu.cs</url>
          <line>136</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryMenu.cs" containingFunctionPresentation="Method 'OnSectorItemSelected'">
            <startOffsets>
              <option value="3934" />
            </startOffsets>
            <endOffsets>
              <option value="3986" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryMenu.cs</url>
          <line>154</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryMenu.cs" containingFunctionPresentation="Method 'OnToolSelected'">
            <startOffsets>
              <option value="4483" />
            </startOffsets>
            <endOffsets>
              <option value="4505" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryItemDescriptionPanel.cs</url>
          <line>78</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryItemDescriptionPanel.cs" containingFunctionPresentation="Method 'SetSelectedResource'">
            <startOffsets>
              <option value="2396" />
            </startOffsets>
            <endOffsets>
              <option value="2432" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryItemDescriptionPanel.cs</url>
          <line>90</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryItemDescriptionPanel.cs" containingFunctionPresentation="Method 'SetSelectedTool'">
            <startOffsets>
              <option value="2733" />
            </startOffsets>
            <endOffsets>
              <option value="2761" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryItemDescriptionPanel.cs</url>
          <line>102</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryItemDescriptionPanel.cs" containingFunctionPresentation="Method 'UpdateDisplay'">
            <startOffsets>
              <option value="3052" />
            </startOffsets>
            <endOffsets>
              <option value="3084" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryItemDescriptionPanel.cs</url>
          <line>158</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryItemDescriptionPanel.cs" containingFunctionPresentation="Method 'OnQuickActionButtonPressed'">
            <startOffsets>
              <option value="4649" />
            </startOffsets>
            <endOffsets>
              <option value="4806" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="68" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/UI/inventory/InventoryItemDescriptionPanel.cs</url>
          <line>108</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\UI\inventory\InventoryItemDescriptionPanel.cs" containingFunctionPresentation="Method 'UpdateDisplay'">
            <startOffsets>
              <option value="3238" />
            </startOffsets>
            <endOffsets>
              <option value="3267" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/scenes/regions/region6/Region6QuestSystem.cs</url>
          <line>17</line>
          <properties documentPath="C:\dev\repos\crafterio\scenes\regions\region6\Region6QuestSystem.cs" containingFunctionPresentation="Method '_Ready'">
            <startOffsets>
              <option value="590" />
            </startOffsets>
            <endOffsets>
              <option value="618" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>